augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs: null
  name: Conv64F_MCL
batch_size: 4
classifier:
  kwargs:
    gamma: 20
    gamma2: 10
    katz_factor: 0.6
    n_k: 3
  name: MCL
data_root: /root/autodl-tmp/Lee/tiered_imagenet
dataloader_num: 1
deterministic: true
device_ids: 0
episode_size: 1
epoch: 50
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.5
    step_size: 10
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    betas:
    - 0.5
    - 0.9
    lr: 0.001
  name: Adam
  other: null
parallel_part:
- emb_func
port: 44746
pretrain_path: null
query_num: 30
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 20.0
test_episode: 1000
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 20000
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 8
