augment: true
augment_method: DeepBdcAugment
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: false
    is_flatten: false
    keep_prob: 1.0
    maxpool_last2: true
  name: resnet12
batch_size: 128
classifier:
  kwargs:
    num_cat: 64
    num_channel: 640
  name: FRN_Pretrain
data_root: /data/fewshot/miniImageNet--ravi
dataloader_num: 1
deterministic: true
device_ids: 7
episode_size: 1
epoch: 350
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/FRN_pretrain.yaml
- backbones/resnet12.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.1
    step_size: 150
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.1
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
port: 44415
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 42
shot_num: 1
tag: null
tb_scale: 1.0
test_episode: 100
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 100
use_memory: false
val_per_epoch: 5
warmup: 0
way_num: 5
workers: 8
