augment: true
augment_method: S2M2Augment
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_flatten: true
    keep_prob: 0.0
    maxpool_last2: true
  name: resnet12
batch_size: 128
classifier:
  kwargs:
    feat_dim: 640
    num_class: 64
  name: MetabaselineKendallPretrain
data_root: ./data/miniImageNet--ravi
dataloader_num: 1
deterministic: true
device_ids: 0
episode_size: 1
epoch: 200
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/MetaBaselineKendall_pretrain.yaml
- backbones/resnet12.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    milestones:
    - 150
  name: MultiStepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.1
    weight_decay: 0.0005
  name: SGD
parallel_part:
- emb_func
port: 33214
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 5
tag: null
tb_scale: 0.5
test_episode: 200
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 100
use_memory: false
val_per_epoch: 5
warmup: 0
way_num: 5
workers: 8
