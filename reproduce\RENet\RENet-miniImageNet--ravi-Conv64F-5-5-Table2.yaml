augment: true
augment_method: null
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    is_feature: false
    is_flatten: false
    last_pool: true
    leaky_relu: false
    maxpool_last2: true
    negative_slope: 0.2
  name: Conv64F
batch_size: 128
classifier:
  kwargs:
    feat_dim: 64
    lambda_epi: 0.25
    num_classes: 64
    temperature: 0.2
    temperature_attn: 5.0
  name: RENet
data_root: /data/miniImageNet--ravi
deterministic: true
device_ids: 3
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/RENet.yaml
- backbones/Conv64F.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    T_max: 100
    eta_min: 0
  name: CosineAnnealingLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.1
    momentum: 0.9
    nesterov: true
    weight_decay: 0.0005
  name: SGD
  other:
    emb_func: 0.1
parallel_part:
- emb_func
pretrain_path: null
query_num: 15
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 0
shot_num: 5
tag: null
tb_scale: 1.5
test_episode: 200
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 300
use_memory: false
way_num: 5
