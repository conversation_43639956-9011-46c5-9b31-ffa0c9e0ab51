includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/SKD.yaml
  - backbones/resnet12.yaml


device_ids: 0
way_num: 5
shot_num: 1
query_num: 15
episode_size: 1
train_episode: 100
test_episode: 100

batch_size: 128

save_part:
  - emb_func
  - cls_classifier

classifier:
  name: SKDModel
  kwargs:
    feat_dim: 1600
    num_class: 64
    gamma: 1.0
    alpha: 0.1
    is_distill: False
    emb_func_path: ./results/SKDModel-miniImageNet--ravi-Conv64F-5-1-Sep-23-2021-15-16-27/checkpoints/emb_func_best.pth
    cls_classifier_path: ./results/SKDModel-miniImageNet--ravi-Conv64F-5-1-Sep-23-2021-15-16-27/checkpoints/cls_classifier_best.pth


backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: False
    negative_slope: 0.2
    last_pool: True
    maxpool_last2: True

# backbone:
#   name: resnet12
#   kwargs:
#     keep_prob: 0.0

# backbone:
#   name: resnet18
#   kwargs:

# backbone:
#   name: WRN
#   kwargs:
#     depth: 10
#     widen_factor: 10
#     dropRate: 0.0
#     avg_pool: True
#     is_flatten: True
