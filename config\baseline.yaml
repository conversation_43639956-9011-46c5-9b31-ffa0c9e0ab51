includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/Baseline.yaml
  - backbones/Conv64F.yaml

classifier:
  name: Baseline
  kwargs:
    inner_param:
      inner_optim:
        name: <PERSON>
        kwargs:
          lr: 1e-3
      inner_train_iter: 100
      inner_batch_size: 4
    feat_dim: 1600
    num_class: 64

backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: False
    negative_slope: 0.2
    last_pool: True

# backbone:
#   name: resnet12
#   kwargs:
#     keep_prob: 0.0
#     avg_pool: True
#     is_flatten: True

# resnet12: feat_dim: 640

# resnet18: feat_dim: 512

# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10

# WRN: feat_dim: 640
