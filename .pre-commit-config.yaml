repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: check-yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: requirements-txt-fixer
      - id: fix-encoding-pragma
  - repo: https://github.com/psf/black
    rev: 21.7b0
    hooks:
      - id: black
        args: [ '--line-length', '100' ]
        language_version: python3
  - repo: https://gitlab.com/pycqa/flake8
    rev: 3.9.2
    hooks:
      - id: flake8
        args: [ '--max-line-length','100','--ignore','E731, E403, W503, E501, E203, F403, F401, E402', ]
