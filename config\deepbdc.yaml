includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml

n_gpu: 1
device_ids: 1
episode_size: 1
epoch: 170
image_size: 84
batch_size: 64

test_episode: 600   # test/val n_episodes, number of episodes in meta val
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 600
query_num: 15
shot_num: 1
way_num: 5

augment: true
augment_times: 1
augment_times_query: 1
augment_method: DeepBdcAugment

backbone:
  name: resnet12Bdc
  kwargs:
    reduce_dim: 640

classifier:
  name: DeepBDC
  kwargs: ~

pretrain_path: ./results/DeepBDC_Pretrain-miniImageNet--ravi-resnet12Bdc-5-1-Feb-08-2023-17-07-27/checkpoints/emb_func_best.pth

optimizer:
  kwargs:
    lr: 0.0001
    momentum: 0.9
    nesterov: true
    weight_decay: 0.0005
  name: SGD
  other: null

lr_scheduler:
  kwargs:
    gamma: 0.1
    milestones:
    - 40
    - 80
  name: MultiStepLR

save_part:
  - emb_func
  - cls_classifier