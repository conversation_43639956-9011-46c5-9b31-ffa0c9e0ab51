augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    is_feature: false
    is_flatten: false
    last_pool: false
    leaky_relu: false
    maxpool_last2: false
    negative_slope: 0.2
  name: Conv64F
batch_size: 128
classifier:
  kwargs:
    feat_dim: 64
    feat_height: 3
    feat_width: 3
  name: RelationNet
data_root: /data/wzy/tiered_imagenet
deterministic: true
device_ids: 2
episode_size: 4
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/RelationNet.yaml
- backbones/Conv64F.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.5
    step_size: 20
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: Adam
  other: null
parallel_part:
- emb_func
port: 27962
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 8.333333333333334
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 5000
use_memory: false
way_num: 5
workers: 32
