augment: true
augment_times: 1
augment_times_query: 1
augment_method: DeepBdcAugment
backbone:
  name: resnet12Bdc
  kwargs:
    reduce_dim: 128
batch_size: 64
classifier:
  name: DeepBDC_Pretrain
  kwargs: 
    reduce_dim: 128
    dropout_rate: 0.5
    val_type: stl
    num_class: 64
    penalty_C: 0.1        # 1-shot(0.1)  5-shot(2.0), as you test stl_model, you should modify this param in result/config.yaml
    is_distill: false     # pretrain or distill
    emb_func_path: ~
    classifier_path: ~
data_root: /home/<USER>/miniImageNet--ravi
deterministic: true
n_gpu: 1
device_ids: 2
episode_size: 1
epoch: 170
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.1
    milestones:
    - 100
    - 150
  name: MultiStepLR

optimizer:
  kwargs:
    lr: 0.05
    momentum: 0.9
    nesterov: true
    weight_decay: 0.0005
  name: SGD
  other: 
    emb_func.bdc_pool.temperature: 0.001
parallel_part:
- emb_func
- classifier
port: 48828
pretrain_path: ~
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
- classifier
seed: 1
tag: null
tb_scale: 3.3333333333333335
# val_per_epoch: 1      # NOTE: save best model
val_per_epoch: 200  # NOTE: just save last model
test_episode: 600   # test/val n_episodes, number of episodes in meta val
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 1000
query_num: 15
shot_num: 1
use_memory: false
way_num: 5
workers: 4