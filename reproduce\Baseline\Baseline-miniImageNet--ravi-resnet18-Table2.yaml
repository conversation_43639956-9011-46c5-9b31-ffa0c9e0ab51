augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_feature: false
    is_flatten: true
    last_block_stride: 2
  name: resnet18
batch_size: 128
classifier:
  kwargs:
    feat_dim: 512
    inner_param:
      inner_batch_size: 4
      inner_optim:
        kwargs:
          dampening: 0.9
          lr: 0.01
          momentum: 0.9
          weight_decay: 0.001
        name: SGD
      inner_train_iter: 100
    num_class: 200
  name: Baseline
data_root: /data/wzy/miniImageNet--ravi
deterministic: true
device_ids: 0
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/Baseline.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 100
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: <PERSON>
parallel_part:
- emb_func
port: 31778
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
resume_path: ./results/Baseline-miniImageNet--ravi-Conv64F-5-5-Dec-01-2021-06-52-22
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 5
tag: null
tb_scale: 1.6666666666666667
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 1000
use_memory: false
way_num: 5
workers: 32
