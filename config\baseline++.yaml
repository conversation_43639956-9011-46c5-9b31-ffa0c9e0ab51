includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/Baseline++.yaml
  - backbones/resnet18.yaml


# few shot settings
way_num: 5
shot_num: 1
query_num: 15
episode_size: 1
train_episode: 100
test_episode: 600

batch_size: 128

classifier:
  name: BaselinePlus
  kwargs:
    inner_param:
      inner_optim:
        name: Adam
        kwargs:
          lr: 1e-2
      inner_train_iter: 100
      inner_batch_size: 4
    feat_dim: 1600
    num_class: 351


backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: False
    negative_slope: 0.2
    last_pool: True

# Conv64: feat_dim: 1600

# backbone:
#   name: resnet12
#   kwargs:
#     keep_prob: 0.0
#     avg_pool: True
#     is_flatten: True

# resnet12: feat_dim: 640

# resnet18: feat_dim: 512

# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10

# WRN: feat_dim: 640
