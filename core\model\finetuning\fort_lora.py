"""
@inproceedings{wang2023focus,
title={Focus Your Attention when Few-Shot Classification},
author={<PERSON><PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>},
booktitle={Thirty-seventh Conference on Neural Information Processing Systems},
year={2023},
url={https://openreview.net/forum?id=uFlE0qgtRO}
}

Adapted from https://github.com/Haoqing-Wang/FORT.
"""
import os
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim
from PIL import Image
import seaborn as sns
import matplotlib.pyplot as plt
from torchvision import transforms
import datetime
import time
import copy
from pathlib import Path
from qpth.qp import QPFunction
from core.utils import accuracy
from .finetuning_model import FinetuningModel


def one_hot(indices, depth):
    """
    Returns a one-hot tensor.
    This is a PyTorch equivalent of Tensorflow's tf.one_hot.
    Parameters:
      indices:  a (n_batch, m) Tensor or (m) Tensor.
      depth: a scalar. Represents the depth of the one hot dimension.
    Returns: a (n_batch, m, depth) Tensor or (m, depth) Tensor.
    """
    encoded_indicies = torch.zeros(indices.size() + torch.Size([depth])).cuda()  # (n_batch, m, depth) or (m, depth)
    index = indices.reshape(indices.size()+torch.Size([1]))  # (n_batch, m, 1) or (m, 1)
    if len(indices.size()) < 2:
        encoded_indicies = encoded_indicies.scatter_(1, index, 1)
    else:
        encoded_indicies = encoded_indicies.scatter_(2, index, 1)
    return encoded_indicies


def kronecker(matrix1, matrix2):
    matrix1_flatten = matrix1.reshape(-1)
    matrix2_flatten = matrix2.reshape(-1)
    return torch.mm(matrix1_flatten.unsqueeze(1), matrix2_flatten.unsqueeze(0)).reshape(list(matrix1.size())+list(matrix2.size())).permute([0, 2, 1, 3]).reshape(matrix1.size(0)*matrix2.size(0), matrix1.size(1)*matrix2.size(1))


class net(nn.Module):
    def __init__(self, encoder, n_way):
        super(net, self).__init__()
        self.n_way = n_way
        self.encoder = encoder
        self.head = nn.Linear(self.encoder.feat_dim, n_way)

        self.hooks = []
        for name, module in self.named_modules():
            if 'attn_drop' in name:
                self.hooks.append(module.register_forward_hook(self.get_attention))
            if 'blocks.11.norm1' in name:
                self.hooks.append(module.register_backward_hook(self.get_gradient))
        self.attentions = []
        self.gradients = []

    def get_attention(self, module, input, output):
        self.attentions.append(output.cpu())

    def get_gradient(self, module, grad_input, grad_output):
        self.gradients.append(grad_input[0].cpu())

    def forward(self, img):  # (B, 3, H, W)
        x, attn = self.encoder(img)
        scores = self.head(x)
        return scores, attn.mean(1)

    def reset_metaoptnet_head(self, xs, temp=20., C_reg=0.1, maxIter=15):
        self.encoder.eval()
        with torch.no_grad():
            support, _ = self.encoder(xs)
        num_support, d = support.size()
        support_labels = torch.from_numpy(np.repeat(range(self.n_way), num_support//self.n_way)).cuda()  # (num_support)

        kernel_matrix = torch.mm(support, support.transpose(0, 1))  # (num_support, num_support)
        id_matrix = torch.eye(self.n_way).cuda()  # (n_way, n_way)
        block_kernel_matrix = kronecker(kernel_matrix, id_matrix)  # (num_support*n_way, num_support*n_way)
        block_kernel_matrix += 1.0 * torch.eye(self.n_way * num_support).cuda()
        support_labels_one_hot = one_hot(support_labels, self.n_way)  # (num_support, n_way)

        G = block_kernel_matrix  # (num_support*n_way, num_support*n_way)
        e = -1. * support_labels_one_hot.flatten()  # (num_support*n_way)
        C = torch.eye(self.n_way * num_support).cuda()  # (num_support*n_way, num_support*n_way)
        h = C_reg * support_labels_one_hot.flatten()  # (num_support*n_way)
        A = kronecker(torch.eye(num_support).cuda(), torch.ones(1, self.n_way).cuda())  # (num_support, num_support*n_way)
        b = torch.zeros(num_support).cuda()  # (num_support, num_support)
        # G, e, C, h, A, b = [x.float() for x in [G, e, C, h, A, b]]
        qp_sol = QPFunction(verbose=False, maxIter=maxIter)(G, e.detach(), C.detach(), h.detach(), A.detach(), b.detach())  # (1, num_support*n_way)
        qp_sol = qp_sol.reshape(num_support, self.n_way)  # (num_support, n_way)

        # weight = (qp_sol*support_labels_one_hot).transpose(0, 1) @ support  # (n_way, d)
        weight = qp_sol.transpose(0, 1) @ support  # (n_way, d)
        # the absolute value of weight is too small,
        # so we use large temp to increase it to be similar as other tunable parameters, which helps to optimize.
        # after this, we need to divide the prediction score by temp, otherwise the fine-tuning loss will be zero.
        state_dict = dict(weight=weight*temp, bias=torch.zeros(self.n_way).cuda())
        self.head.load_state_dict(state_dict)


def get_first_comp(inp):  # cleaner
    inp[torch.isnan(inp)] = 0.
    inp = inp - inp.mean(1, keepdim=True)
    U, S, V = torch.svd(inp, some=False)
    projection = inp @ V[:, :, :1]
    return projection.squeeze()


def importance(net, inp, lab, lamb=1.):
    net.zero_grad()
    output, _ = net(inp)
    category_mask = torch.zeros(output.size()).to(output.device)
    category_mask = category_mask.scatter_(1, lab.unsqueeze(1), 1)
    logit = (output * category_mask).sum(-1).mean()
    logit.backward()
    net.zero_grad()
    attns, grads = net.attentions, net.gradients

    grad = get_first_comp(grads[0][:, 1:].cpu())

    with torch.no_grad():
        result = torch.eye(attns[0].size(-1)-1).unsqueeze(0).to(attns[0].device)  # (1, L, L)
        for attn in attns:
            attn_fused = attn.min(1)[0][:, 1:, 1:] + lamb * grad.unsqueeze(1)
            _, indices = attn_fused.topk(int(attn_fused.size(-1) * 0.9), -1, False)
            attn_fused = attn_fused.scatter_(-1, indices, 0)

            I = torch.eye(attn_fused.size(-1)).unsqueeze(0).to(attn_fused.device)
            a = (attn_fused + I) / 2.
            a = a / a.sum(dim=-1, keepdim=True)
            result = a @ result
    imp = result.mean(1)

    # del hook
    del net.attentions, net.gradients
    for hook in net.hooks:
        hook.remove()

    return imp.cuda()


def imp_to_focus(imp, P):
    _, ids_shuffle = torch.sort(imp, descending=True, dim=1)
    ids_restore = torch.argsort(ids_shuffle, dim=1)
    focus = torch.ones_like(imp)
    focus[:, P:] = 0
    focus = torch.gather(focus, dim=1, index=ids_restore)

    focus = imp * focus
    focus = focus * P / focus.sum(-1, keepdim=True)
    return focus


def ce_loss(y_pred, y_true):
    return - (y_true * F.log_softmax(y_pred, dim=-1)).sum(dim=-1).mean()


class FORT_LoRA(FinetuningModel):
    def __init__(self, n_way, n_support, ft_lr, ft_epoch, tau, alpha, P=14, temp=20., **kwargs):
        super(FORT_LoRA, self).__init__(**kwargs)
        self.temp = temp
        self.n_way = n_way
        self.P = P
        self.ft_lr = ft_lr
        self.ft_epoch = ft_epoch
        self.n_support = n_support
        self.tau = tau
        self.alpha = alpha
        
        # if hasattr(self, 'config') and 'pretrain_path' in self.config:
        #     pretrain_path = self.config['pretrain_path']
        #     if pretrain_path:
        #         print(f"Loading ViT backbone from {pretrain_path}")
        #         checkpoint = torch.load(pretrain_path, map_location="cpu")
        #         if 'model' in checkpoint.keys():
        #             checkpoint = checkpoint['model']
        #         if 'state_dict' in checkpoint.keys():
        #             checkpoint = checkpoint['state_dict']
        #         self.emb_func.load_state_dict(checkpoint, strict=False)
        #         print("Successfully loaded pretrained weights")
                

    def set_forward_loss(self, batch):
        # no need to implement in this method
        raise NotImplementedError


    def set_forward(self, batch):
        image, global_target = batch
        image = image.to(self.device)
        global_target = global_target.to(self.device)

        xs, xq, ys, yq = self.split_by_episode(image, mode=2)
        episode_size = xs.size(0)

        output_list = []
        for i in range(episode_size):
            output = self.set_forward_adaptation(xs[i], ys[i], xq[i])
            output_list.append(output)

        output = torch.cat(output_list, dim=0)
        acc = accuracy(output, yq.reshape(-1))
        print(f"acc for the batch(episode): {acc}%")

        return output, acc

    
    def set_forward_adaptation(self, xs, ys, xq):
        model = net(copy.deepcopy(self.emb_func), self.n_way).to(self.device)
        model.reset_metaoptnet_head(xs, self.temp)
        model.attentions, model.gradients = [], []
        imp = importance(model, xs, ys)
        focus = imp_to_focus(imp, self.P)

        # finetune
        model.train()
        batch_size = self.n_way
        support_size = self.n_way * self.n_support
        loss_fn = nn.CrossEntropyLoss().to(self.device)

        parameters = []
        for n, p in model.named_parameters():
            if('lora_' in n) or ('head' in n):
                parameters.append(p)
        opt = torch.optim.AdamW(parameters, lr=self.ft_lr)

        for epoch in range(self.ft_epoch):
            rand_id = np.random.permutation(support_size)
            for j in range(0, support_size, batch_size):
                opt.zero_grad()
                selected_id = torch.from_numpy(rand_id[j: min(j+batch_size, support_size)]).to(self.device)
                x_batch = xs[selected_id]
                y_batch = ys[selected_id]
                focus_batch = focus[selected_id]

                scores, attn = model(x_batch)
                focus_batch = focus_batch.unsqueeze(1).expand_as(attn).reshape(-1, attn.size(-1))
                attn = attn.reshape(-1, attn.size(-1)) / self.tau
                loss = loss_fn(scores/self.temp, y_batch) + self.alpha * ce_loss(attn, focus_batch)
                loss.backward()
                opt.step()
        del opt
        torch.cuda.empty_cache()

        # test
        model.eval()
        with torch.no_grad():
            scores, _ = model(xq)
        
        del model
        torch.cuda.empty_cache()
        return scores