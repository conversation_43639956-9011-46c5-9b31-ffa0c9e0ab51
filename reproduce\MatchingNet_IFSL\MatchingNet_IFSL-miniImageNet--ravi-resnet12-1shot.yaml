augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_flatten: true
    keep_prob: 0.0
  name: resnet12
batch_size: 128
classifier:
  kwargs:
    feat_dim: 640
    ifsl_param:
      approx: false
      class_num: 64
      cls_path: classifier_best.pth
      d_feature: ed
      feature_path: resnet12.npy
      fusion: concat
      is_cosine_feature: true
      logit_fusion: product
      n_splits: 8
      normalize_before_center: true
      normalize_d: false
      normalize_ed: false
      preprocess_after_split: none
      preprocess_before_split: none
      single: true
      sum_log: true
      temp: 5
      use_counterfactual: false
      use_x_only: false
      x_zero: false
    inner_param:
      iter: 100
      lr: 0.005
    num_classes: 64
    use_MTL: true
  name: MTL
data_root: miniImageNet--ravi
dataloader_num: 1
deterministic: true
device_ids: 7
episode_size: 1
epoch: 10
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 20
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: Adam
  other: null
parallel_part:
- emb_func
port: 36627
pretrain_path: emb_func_best.pth
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 3.3333333333333335
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 2000
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 8
