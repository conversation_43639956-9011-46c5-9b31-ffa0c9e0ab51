augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_flatten: true
    keep_prob: 0.0
    maxpool_last2: true
  name: resnet12
batch_size: 4
classifier:
  kwargs: null
  name: MetaBaselineKendall
data_root: ../LibFewShot/dataset_folder/miniImageNet--ravi
dataloader_num: 1
deterministic: true
device_ids: 0,1
episode_size: 2
epoch: 40
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 10
  name: StepLR
n_gpu: 2
optimizer:
  kwargs:
    lr: 0.001
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
parallel_part:
- emb_func
- classifier
port: 31691
pretrain_path: results/MetabaselineKendallPretrain-miniImageNet--ravi-resnet12-5-5-Jun-02-2024-20-21-31/checkpoints/emb_func_best.pth
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 5
save_part:
- emb_func
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 0.16666666666666666
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 100
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 16
