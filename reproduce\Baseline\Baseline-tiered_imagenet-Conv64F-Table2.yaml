augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    is_feature: false
    is_flatten: true
    last_pool: true
    leaky_relu: false
    negative_slope: 0.2
  name: Conv64F
batch_size: 128
classifier:
  kwargs:
    feat_dim: 1600
    inner_param:
      inner_batch_size: 4
      inner_optim:
        kwargs:
          dampening: 0.9
          lr: 0.01
          momentum: 0.9
          weight_decay: 0.001
        name: SGD
      inner_train_iter: 100
    num_class: 400
  name: Baseline
data_root: /data/wzy/tiered_imagenet
deterministic: true
device_ids: 1
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/Baseline.yaml
- backbones/Conv64F.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 100
  name: Step<PERSON>
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: Adam
parallel_part:
- emb_func
port: 35001
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
resume_path: ./results/Baseline-miniImageNet--ravi-Conv64F-5-5-Dec-01-2021-06-52-22
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 1.6666666666666667
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 1000
use_memory: false
way_num: 5
workers: 32
