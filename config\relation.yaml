includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/RelationNet.yaml
  - backbones/Conv64F.yaml

classifier:
  name: RelationNet
  kwargs:
    feat_dim: 64
    feat_height: 3
    feat_width: 3

backbone:
  name: Conv64F
  kwargs:
    is_flatten: False
    is_feature: False
    leaky_relu: False
    negative_slope: 0.2
    last_pool: False
    maxpool_last2: False

device_ids: 2
episode_size: 1
