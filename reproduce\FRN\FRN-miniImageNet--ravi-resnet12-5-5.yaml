augment: true
augment_method: DeepBdcAugment
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: false
    is_flatten: false
    keep_prob: 1.0
    maxpool_last2: true
  name: resnet12
batch_size: 128
classifier:
  kwargs: null
  name: FRN
data_root: /data/fewshot/miniImageNet--ravi/
dataloader_num: 1
deterministic: true
device_ids: 7
episode_size: 2
epoch: 160
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/FRN.yaml
- backbones/resnet12.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.1
    milestones:
    - 70
    - 120
  name: MultiStepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
    momentum: 0.9
    nesterov: true
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
port: 28817
pretrain_path: ./results/FRN_Pretrain-miniImageNet--ravi-resnet12-5-1-Oct-03-2023-16-26-39/checkpoints/emb_func_best.pth
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 5
tag: null
tb_scale: 1.0
test_episode: 500
test_epoch: 1
test_query: 15
test_shot: 5
test_way: 5
train_episode: 500
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 8
