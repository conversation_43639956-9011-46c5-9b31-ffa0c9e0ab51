augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs: null
  name: resnet12_r2d2
batch_size: 1
classifier:
  kwargs: null
  name: R2D2
data_root: /root/autodl-tmp/Lee/tiered_imagenet
dataloader_num: 1
deterministic: true
device_ids: 0
episode_size: 1
epoch: 1
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/R2D2.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0
    step_size: 10
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0
    momentum: 0.9
    nesterov: true
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
port: 40620
pretrain_path: /root/Lee/tieredimagenet-e0.pth
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 2147483647
shot_num: 5
tag: null
tb_scale: 1.0
test_episode: 200
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 200
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 8
