includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml

deterministic: False

way_num: 5
shot_num: 5
query_num: 15
test_way: 5 # use ~ -> test_* = *_num
test_shot: 5
test_query: 15

episode_size: 1

backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: True
    negative_slope: 0.2
    last_pool: True

classifier:
  name: VERSA
  kwargs:
    feat_dim: 1600
    d_theta: 256
    sample_num: 10
    drop_rate: 0.5


# backbone:
#   name: resnet12
#   kwargs: ~

# classifier:
#   name: VERSA
#   kwargs:
#     feat_dim: 640
#     hid_dim: 640
#     sample_num: 10


# backbone:
#   name: resnet18
#   kwargs: ~

# classifier:
#   name: VERSA
#   kwargs:
#     feat_dim: 512
#     hid_dim: 512
#     sample_num: 10


# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10

# classifier:
#   name: VERSA
#   kwargs:
#     feat_dim: 640
#     hid_dim: 640
#     sample_num: 10
