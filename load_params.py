import torch
from core.model.backbone.vit_lora import vit_base_patch16
from core.model.finetuning.fort_lora import FORT_LoRA

# 创建与实际使用相同结构的模型
backbone = vit_base_patch16()
checkpoint = torch.load('/root/dino_vitbase16_pretrain.pth', map_location='cpu')
if 'model' in checkpoint.keys():
    checkpoint = checkpoint['model']
if 'state_dict' in checkpoint.keys():
    checkpoint = checkpoint['state_dict']

# Load parameters
msg = backbone.load_state_dict(checkpoint, strict=False)

model = FORT_LoRA(
    n_way=20,
    n_support=1, 
    ft_lr=5e-3, 
    ft_epoch=10,
    tau=1.,
    alpha=5e-1,
    P=14,
    temp=20.,
    emb_func=backbone
)

# 保存模型权重
torch.save(model.state_dict(), './results/FORT_LoRA/checkpoints/model_best.pth')