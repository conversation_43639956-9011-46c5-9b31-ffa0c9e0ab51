augment: true
augment_times: 1
backbone:
  kwargs: null
  name: resnet12
batch_size: 64
classifier:
  kwargs:
    alpha: 0.5
    feat_dim: 640
    gamma: 0.5
    is_distill: false
    num_class: 64
  name: RFSModel
data_name: miniImageNet
data_root: /data1/miniImageNet--ravi
deterministic: false
device_ids: 1
episode_size: 5
epoch: 100
image_size: 84
log_interval: 100
log_level: info
lr_scheduler:
  kwargs:
    gamma: 0.1
    milestones:
    - 60
    - 80
  name: MultiStepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.05
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
- classifier
- distill_layer
pretrain_path: null
query_num: 15
result_root: ./results
resume: false
save_interval: 20
save_part:
- emb_func
- classifier
seed: 1
shot_num: 1
test_episode: 1000
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 5000
use_memory: false
way_num: 5
