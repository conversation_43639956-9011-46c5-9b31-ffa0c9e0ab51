augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    is_feature: false
    is_flatten: true
    last_pool: true
    leaky_relu: false
    negative_slope: 0.2
  name: Conv64F
batch_size: 128
classifier:
  kwargs:
    alpha: 0.5
    classifier_path: ./results/RFSModel-miniImageNet--ravi-resnet18-5-1-Sep-23-2021-15-24-44/checkpoints/classifier_best.pth
    emb_func_path: ./results/RFSModel-miniImageNet--ravi-resnet18-5-1-Sep-23-2021-15-24-44/checkpoints/emb_func_best.pth
    feat_dim: 1600
    gamma: 0.5
    is_distill: false
    num_class: 351
  name: RFSModel
data_root: /data/wzy/tiered_imagenet
deterministic: true
device_ids: 1
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/RFS.yaml
- backbones/resnet18.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    T_max: 100
    eta_min: 0
  name: CosineAnnealingLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.1
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
- classifier
- distill_layers
pretrain_path: null
query_num: 15
result_root: ./results
resume: true
resume_path: ./results/RFSModel-tiered_imagenet-Conv64F-5-1-Nov-27-2021-14-05-21
save_interval: 10
save_part:
- emb_func
seed: 0
shot_num: 1
tag: null
tb_scale: 8.333333333333334
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 5000
use_memory: false
way_num: 5
