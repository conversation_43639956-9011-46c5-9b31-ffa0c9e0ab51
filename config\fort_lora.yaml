includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml

classifier:
  name: FORT_LoRA
  kwargs:
    n_way: 20
    n_support: 1
    ft_lr: 5e-3
    ft_epoch: 10
    tau: 1.
    alpha: 5e-1
    P: 14
    temp: 20.

backbone:
  name: vit_base_patch16
  kwargs: ~

image_size: 224
pretrain_path: /root/dino_vitbase16_pretrain.pth
way_num: 20
shot_num: 1
query_num: 15
device_ids: 0
n_gpu: 1
