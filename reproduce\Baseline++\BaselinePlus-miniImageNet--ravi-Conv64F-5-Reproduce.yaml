augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    is_feature: false
    is_flatten: true
    last_pool: true
    leaky_relu: false
    negative_slope: 0.2
  name: Conv64F
batch_size: 16
classifier:
  kwargs:
    feat_dim: 1600
    inner_param:
      inner_batch_size: 4
      inner_optim:
        kwargs:
          dampening: 0.9
          lr: 0.01
          momentum: 0.9
          weight_decay: 0.001
        name: SGD
      inner_train_iter: 100
    num_class: 64
  name: BaselinePlus
data_root: /data/wzy/miniImageNet--ravi
deterministic: true
device_ids: 1
episode_size: 1
epoch: 400
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/Baseline++.yaml
- backbones/Conv64F.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 100
  name: <PERSON><PERSON>
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: Adam
  other: null
parallel_part:
- emb_func
pretrain_path: null
query_num: 15
result_root: ./results
resume: false
save_interval: 50
save_part:
- emb_func
seed: 0
shot_num: 5
tag: null
tb_scale: 100.0
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 5
test_way: 5
train_episode: 60000
use_memory: false
way_num: 5
