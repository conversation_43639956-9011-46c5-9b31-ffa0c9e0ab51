includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/RFS.yaml
  - backbones/resnet18.yaml

device_ids: 0
way_num: 5
shot_num: 1
query_num: 15
episode_size: 1
train_episode: 100
test_episode: 100

save_part:
  - emb_func
  - classifier

classifier:
  name: RFSModel
  kwargs:
    feat_dim: 512
    num_class: 64
    gamma: 0.5
    alpha: 0.5
    is_distill: False
    emb_func_path: ./results/RFSModel-miniImageNet--ravi-resnet18-5-1-Sep-23-2021-15-24-44/checkpoints/emb_func_best.pth
    classifier_path: ./results/RFSModel-miniImageNet--ravi-resnet18-5-1-Sep-23-2021-15-24-44/checkpoints/classifier_best.pth


# backbone:
#   name: Conv64F
#   kwargs:
#     is_flatten: True
#     is_feature: False
#     leaky_relu: False
#     negative_slope: 0.2
#     last_pool: True

# Conv64: feat_dim: 1600

# backbone:
#   name: resnet12
#   kwargs:
#     keep_prob: 0.0
#     avg_pool: True
#     is_flatten: True

# resnet12: feat_dim: 640

# resnet18: feat_dim: 512

# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10

# WRN: feat_dim: 640
