includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml
  - classifiers/R2D2.yaml

way_num: 5
shot_num: 1
query_num: 15
test_way: 5 # use ~ -> test_* = *_num
test_shot: 1
test_query: 15

backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: True
    negative_slope: 0.2
    last_pool: True

# backbone:
#   name: resnet12
#   kwargs: ~

# backbone:
#   name: resnet18
#   kwargs: ~

# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10
