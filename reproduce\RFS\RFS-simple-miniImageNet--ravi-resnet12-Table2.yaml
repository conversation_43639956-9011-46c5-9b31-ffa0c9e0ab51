augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_flatten: true
    keep_prob: 0.0
    maxpool_last2: true
  name: resnet12
batch_size: 64
classifier:
  kwargs:
    alpha: 0.5
    feat_dim: 640
    gamma: 0.5
    is_distill: false
    num_class: 64
  name: RFSModel
data_name: miniImageNet
data_root: /data/wzy/miniImageNet--ravi
deterministic: false
device_ids: 1
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 0.1
    milestones:
    - 60
    - 80
  name: MultiStepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.05
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
- classifier
- distill_layer
port: 45832
pretrain_path: null
query_num: 15
rank: 0
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
- classifier
seed: 2147483647
shot_num: 1
tag: null
tb_scale: 5.0
test_episode: 1000
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 5000
use_memory: false
way_num: 5
workers: 8
