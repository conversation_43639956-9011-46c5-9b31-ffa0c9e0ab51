includes:
  - headers/data.yaml
  - headers/device.yaml
  - headers/misc.yaml
  - headers/model.yaml
  - headers/optimizer.yaml

way_num: 5
shot_num: 1
query_num: 15
episode_size: 2
train_episode: 2000
test_episode: 600

device_ids: 5
n_gpu: 1
epoch: 100

optimizer:
  name: <PERSON>
  kwargs:
    lr: 1e-3
  other: ~

backbone:
  name: Conv64F
  kwargs:
    is_flatten: True
    is_feature: False
    leaky_relu: False
    negative_slope: 0.2
    last_pool: True

classifier:
  name: MAML
  kwargs:
    inner_param:
      lr: 1e-2
      train_iter: 5
      test_iter: 10
    feat_dim: 1600


# backbone:
#   name: resnet12
#   kwargs: ~

# classifier:
#   name: MAML
#   kwargs:
#     inner_param:
#       lr: 1e-2
#       train_iter: 5
#       test_iter: 10
#     feat_dim: 640


# backbone:
#   name: resnet18
#   kwargs: ~

# classifier:
#   name: MAML
#   kwargs:
#     inner_param:
#       lr: 1e-2
#       train_iter: 5
#       test_iter: 10
#     feat_dim: 512


# backbone:
#   name: WRN
#   kwargs:
#     depth: 28
#     widen_factor: 10

# classifier:
#   name: MAML
#   kwargs:
#     inner_param:
#       lr: 1e-2
#       train_iter: 5
#       test_iter: 10
#     feat_dim: 640
