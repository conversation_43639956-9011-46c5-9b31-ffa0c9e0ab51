augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs:
    avg_pool: true
    is_flatten: true
    keep_prob: 0.0
  name: resnet12
batch_size: 128
classifier:
  kwargs:
    feat_dim: 640
    inner_param:
      inner_batch_size: 4
      inner_optim:
        kwargs:
          dampening: 0.9
          lr: 0.01
          momentum: 0.9
          weight_decay: 0.001
        name: SGD
      inner_train_iter: 100
    num_class: 64
  name: BaselinePlus
data_root: /data/sby/Datasets/miniImageNet--ravi
deterministic: true
device_ids: 1
episode_size: 1
epoch: 100
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
- classifiers/Baseline++.yaml
- backbones/resnet12.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    T_max: 100
    eta_min: 0
  name: CosineAnnealingLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.01
    momentum: 0.9
    weight_decay: 0.0005
  name: SGD
  other: null
parallel_part:
- emb_func
pretrain_path: null
query_num: 15
result_root: ./results
resume: false
save_interval: 10
save_part:
- emb_func
seed: 0
shot_num: 1
tag: null
tb_scale: 0.16666666666666666
test_episode: 600
test_epoch: 5
test_query: 15
test_shot: 1
test_way: 5
train_episode: 100
use_memory: false
way_num: 5
