augment: true
augment_times: 1
augment_times_query: 1
backbone:
  kwargs: null
  name: resnet18
batch_size: 64
augment_method: S2M2Augment
classifier:
  kwargs:
    feat_dim: 512
    inner_param:
      inner_batch_size: 4
      inner_optim:
        kwargs:
          dampening: 0.9
          lr: 0.01
          momentum: 0.9
          weight_decay: 0.001
        name: SGD
      inner_train_iter: 300
    num_class: 64
  name: S2M2
data_root: /data/fewshot/miniImageNet--ravi
dataloader_num: 1
deterministic: true
device_ids: 0
episode_size: 1
epoch: 400
image_size: 84
includes:
- headers/data.yaml
- headers/device.yaml
- headers/misc.yaml
- headers/model.yaml
- headers/optimizer.yaml
log_interval: 100
log_level: info
log_name: null
log_paramerter: false
lr_scheduler:
  kwargs:
    gamma: 1.0
    step_size: 100
  name: StepLR
n_gpu: 1
optimizer:
  kwargs:
    lr: 0.001
  name: Adam
  other: null
parallel_part:
- emb_func
port: 31401
pretrain_path: null
query_num: 15
rank: 0
result_root: ./re
resume: false
save_interval: 50
save_part:
- emb_func
seed: 0
shot_num: 1
tag: null
tb_scale: 300.0
test_episode: 200
test_epoch: 10
test_query: 15
test_shot: 1
test_way: 5
train_episode: 60000
use_memory: false
val_per_epoch: 1
warmup: 0
way_num: 5
workers: 8
